# TikTok Automation Desktop App

A powerful desktop application for TikTok automation with antidetect browser capabilities, built with Electron and Python.

## Features

- 🎭 **Antidetect Browser Profiles** - Create and manage browser profiles with advanced fingerprint spoofing
- 🌐 **Multi-Proxy Support** - HTTP, HTTPS, SSH, SOCKS5 proxy integration
- 🍪 **<PERSON>ie Persistence** - Automatic TikTok login cookie management
- 🎯 **Competitor Analysis** - Follow competitors' followers automatically
- 📊 **Real-time Monitoring** - Live progress tracking and analytics
- 🔒 **Security First** - Encrypted data storage and secure automation
- ⚡ **Performance Optimized** - Memory efficient with smart resource management

## Architecture

```
TikTok Automation Desktop App
├── Electron Main Process (Node.js)
│   ├── Window Management
│   ├── System Integration
│   └── Backend Communication
├── React Frontend
│   ├── Dashboard UI
│   ├── Profile Management
│   ├── Real-time Monitoring
│   └── Settings
├── Python Backend (FastAPI)
│   ├── Camoufox Integration
│   ├── TikTok Automation Engine
│   ├── Database Management
│   └── WebSocket Server
└── SQLite Database
    ├── Browser Profiles
    ├── TikTok Accounts
    ├── Automation Tasks
    └── Activity Logs
```

## Tech Stack

### Frontend
- **Electron** - Desktop app framework
- **React 18** - UI framework
- **Material-UI** - Component library
- **Socket.IO** - Real-time communication
- **Zustand** - State management
- **React Query** - Data fetching

### Backend
- **FastAPI** - Python web framework
- **SQLAlchemy** - Database ORM
- **WebSockets** - Real-time communication
- **Camoufox** - Antidetect browser
- **Celery** - Background task queue
- **Redis** - Caching and message broker

## Installation

### Prerequisites
- Node.js 18+
- Python 3.9+
- Git

### Development Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd tiktok-automation
```

2. **Setup Python virtual environment**
```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
# venv\Scripts\activate
```

3. **Install dependencies**
```bash
# Install Node.js dependencies
npm install

# Install Python dependencies (make sure virtual environment is activated)
cd backend
pip install -r requirements.txt
cd ..

# Install frontend dependencies
cd frontend
npm install
cd ..
```

3. **Setup environment**
```bash
# Copy environment template
cp backend/.env.example backend/.env

# Edit configuration
nano backend/.env
```

5. **Initialize database**
```bash
# Make sure virtual environment is activated
source venv/bin/activate  # On macOS/Linux
# venv\Scripts\activate   # On Windows

cd backend
python -m alembic upgrade head
cd ..
```

6. **Start development server**
```bash
# Make sure virtual environment is activated
source venv/bin/activate  # On macOS/Linux
# venv\Scripts\activate   # On Windows

npm run dev
```

## Building for Production

### Build all components
```bash
npm run build
```

### Create distributables
```bash
# Windows
npm run dist:win

# macOS
npm run dist:mac

# Linux
npm run dist:linux

# All platforms
npm run dist
```

## Project Structure

```
tiktok-automation/
├── src/main/                 # Electron main process
│   ├── main.js              # Main application entry
│   └── preload.js           # Preload script for security
├── frontend/                # React frontend
│   ├── src/
│   │   ├── components/      # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── store/          # State management
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
├── backend/                 # Python backend
│   ├── core/               # Core configuration
│   ├── models/             # Database models
│   ├── api/                # API routes
│   ├── services/           # Business logic
│   ├── automation/         # TikTok automation
│   └── camoufox_integration/ # Browser integration
├── assets/                 # App icons and resources
└── dist/                   # Build output
```

## Configuration

### Environment Variables

Create `backend/.env` file:

```env
# App Settings
DEBUG=false
SECRET_KEY=your-secret-key-here

# Database
DATABASE_URL=sqlite:///./tiktok_automation.db

# TikTok Settings
TIKTOK_FOLLOW_LIMIT_PER_HOUR=50
TIKTOK_FOLLOW_LIMIT_PER_DAY=200

# Performance
MAX_CONCURRENT_BROWSERS=3
MEMORY_LIMIT_MB=1024
```

## Usage

### 1. Create Browser Profile
- Configure proxy settings (HTTP/HTTPS/SSH/SOCKS5)
- Set fingerprint parameters
- Save profile for reuse

### 2. Add TikTok Account
- Login to TikTok using antidetect browser
- Cookies are automatically saved
- Account ready for automation

### 3. Setup Competitor Tracking
- Add competitor TikTok profiles
- Configure follow settings
- Set automation schedule

### 4. Monitor Progress
- Real-time dashboard
- Activity logs
- Performance metrics

## Security Features

- **Encrypted Cookie Storage** - All cookies encrypted at rest
- **Proxy Validation** - Automatic proxy health checks
- **Rate Limiting** - Intelligent request throttling
- **Fingerprint Rotation** - Dynamic browser fingerprints
- **Anti-Detection** - Human-like behavior patterns

## Performance Optimizations

- **Memory Management** - Smart garbage collection
- **Process Isolation** - Separate processes for heavy tasks
- **Lazy Loading** - Load components on demand
- **Resource Pooling** - Efficient browser instance management
- **Caching** - Intelligent data caching strategies

## Troubleshooting

### Common Installation Issues

#### "externally-managed-environment" Error
If you encounter this error when installing Python packages:
```bash
# Solution 1: Use virtual environment (recommended)
python3 -m venv venv
source venv/bin/activate  # On macOS/Linux
# venv\Scripts\activate   # On Windows
pip install -r requirements.txt

# Solution 2: Use pipx for global installation
brew install pipx
pipx install <package-name>

# Solution 3: Use --user flag (not recommended for development)
pip install --user -r requirements.txt
```

#### Package Version Conflicts
If you encounter "No matching distribution found" errors:
```bash
# Update pip to latest version
pip install --upgrade pip

# Install with flexible version resolution
pip install -r requirements.txt --upgrade

# If specific package fails, install without version constraints
pip install package-name --upgrade

# Clear pip cache if needed
pip cache purge
```

#### Virtual Environment Activation Issues
```bash
# If activation fails, try:
# On macOS/Linux:
chmod +x venv/bin/activate
source venv/bin/activate

# On Windows PowerShell:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
venv\Scripts\Activate.ps1
```

#### Node.js Version Issues
```bash
# Check Node.js version
node --version

# Install Node.js 18+ using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

## Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Join our Discord community

## Disclaimer

This software is for educational and research purposes only. Users are responsible for complying with TikTok's Terms of Service and applicable laws.
