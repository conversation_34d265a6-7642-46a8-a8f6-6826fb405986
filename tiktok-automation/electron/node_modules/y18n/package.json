{"name": "y18n", "version": "5.0.8", "description": "the bare-bones internationalization library used by yargs", "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": "yargs/y18n", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "./build/index.cjs", "scripts": {"check": "standardx **/*.ts **/*.cjs **/*.mjs", "fix": "standardx --fix **/*.ts **/*.cjs **/*.mjs", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^3.0.0", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^7.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "files": ["build", "index.mjs", "!*.d.ts"], "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}}