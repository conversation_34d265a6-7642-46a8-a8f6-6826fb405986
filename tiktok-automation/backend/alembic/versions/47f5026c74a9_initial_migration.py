"""Initial migration

Revision ID: 47f5026c74a9
Revises: 
Create Date: 2025-07-03 13:14:16.783142

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '47f5026c74a9'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('browser_profiles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('proxy_id', sa.Integer(), nullable=True),
    sa.Column('fingerprint_config', sa.JSO<PERSON>(), nullable=False),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('navigator_config', sa.JSON(), nullable=True),
    sa.Column('screen_config', sa.JSON(), nullable=True),
    sa.Column('window_config', sa.JSON(), nullable=True),
    sa.Column('webgl_config', sa.JSON(), nullable=True),
    sa.Column('audio_config', sa.JSON(), nullable=True),
    sa.Column('geolocation_config', sa.JSON(), nullable=True),
    sa.Column('timezone', sa.String(length=100), nullable=True),
    sa.Column('locale', sa.String(length=20), nullable=True),
    sa.Column('fonts_config', sa.JSON(), nullable=True),
    sa.Column('browser_settings', sa.JSON(), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=False),
    sa.Column('last_used', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_browser_profiles_id'), 'browser_profiles', ['id'], unique=False)
    op.create_index(op.f('ix_browser_profiles_name'), 'browser_profiles', ['name'], unique=False)
    op.create_index(op.f('ix_browser_profiles_proxy_id'), 'browser_profiles', ['proxy_id'], unique=False)
    op.create_table('competitors',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=255), nullable=False),
    sa.Column('display_name', sa.String(length=255), nullable=True),
    sa.Column('tiktok_url', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_monitoring', sa.Boolean(), nullable=False),
    sa.Column('profile_picture_url', sa.Text(), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('follower_count', sa.Integer(), nullable=False),
    sa.Column('following_count', sa.Integer(), nullable=False),
    sa.Column('likes_count', sa.Integer(), nullable=False),
    sa.Column('videos_count', sa.Integer(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('is_private', sa.Boolean(), nullable=False),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('total_followers_scraped', sa.Integer(), nullable=False),
    sa.Column('last_follower_scrape', sa.DateTime(), nullable=True),
    sa.Column('follower_scrape_count', sa.Integer(), nullable=False),
    sa.Column('total_following_scraped', sa.Integer(), nullable=False),
    sa.Column('last_following_scrape', sa.DateTime(), nullable=True),
    sa.Column('following_scrape_count', sa.Integer(), nullable=False),
    sa.Column('auto_follow_followers', sa.Boolean(), nullable=False),
    sa.Column('auto_follow_following', sa.Boolean(), nullable=False),
    sa.Column('follow_limit_per_day', sa.Integer(), nullable=False),
    sa.Column('follow_delay_min', sa.Integer(), nullable=False),
    sa.Column('follow_delay_max', sa.Integer(), nullable=False),
    sa.Column('filter_criteria', sa.JSON(), nullable=True),
    sa.Column('total_follows_attempted', sa.Integer(), nullable=False),
    sa.Column('total_follows_successful', sa.Integer(), nullable=False),
    sa.Column('total_follows_failed', sa.Integer(), nullable=False),
    sa.Column('error_count', sa.Integer(), nullable=False),
    sa.Column('last_error', sa.Text(), nullable=True),
    sa.Column('last_error_time', sa.DateTime(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('last_checked', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_competitors_id'), 'competitors', ['id'], unique=False)
    op.create_index(op.f('ix_competitors_username'), 'competitors', ['username'], unique=True)
    op.create_table('proxies',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('proxy_type', sa.Enum('HTTP', 'HTTPS', 'SOCKS4', 'SOCKS5', 'SSH', name='proxytype'), nullable=False),
    sa.Column('host', sa.String(length=255), nullable=False),
    sa.Column('port', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=255), nullable=True),
    sa.Column('password', sa.String(length=255), nullable=True),
    sa.Column('ssh_private_key', sa.Text(), nullable=True),
    sa.Column('ssh_passphrase', sa.String(length=255), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'ERROR', 'TESTING', name='proxystatus'), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('response_time_ms', sa.Integer(), nullable=True),
    sa.Column('success_rate', sa.Integer(), nullable=False),
    sa.Column('total_requests', sa.Integer(), nullable=False),
    sa.Column('failed_requests', sa.Integer(), nullable=False),
    sa.Column('country', sa.String(length=100), nullable=True),
    sa.Column('city', sa.String(length=100), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('last_checked', sa.DateTime(), nullable=True),
    sa.Column('last_error', sa.Text(), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=False),
    sa.Column('last_used', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_proxies_id'), 'proxies', ['id'], unique=False)
    op.create_index(op.f('ix_proxies_name'), 'proxies', ['name'], unique=False)
    op.create_index(op.f('ix_proxies_proxy_type'), 'proxies', ['proxy_type'], unique=False)
    op.create_table('user_settings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('theme', sa.String(length=20), nullable=False),
    sa.Column('language', sa.String(length=10), nullable=False),
    sa.Column('auto_save_cookies', sa.Boolean(), nullable=False),
    sa.Column('auto_start_tasks', sa.Boolean(), nullable=False),
    sa.Column('auto_retry_failed_tasks', sa.Boolean(), nullable=False),
    sa.Column('follow_limit_per_hour', sa.Integer(), nullable=False),
    sa.Column('follow_limit_per_day', sa.Integer(), nullable=False),
    sa.Column('unfollow_limit_per_hour', sa.Integer(), nullable=False),
    sa.Column('unfollow_limit_per_day', sa.Integer(), nullable=False),
    sa.Column('like_limit_per_hour', sa.Integer(), nullable=False),
    sa.Column('like_limit_per_day', sa.Integer(), nullable=False),
    sa.Column('delay_between_follows_min', sa.Integer(), nullable=False),
    sa.Column('delay_between_follows_max', sa.Integer(), nullable=False),
    sa.Column('delay_between_unfollows_min', sa.Integer(), nullable=False),
    sa.Column('delay_between_unfollows_max', sa.Integer(), nullable=False),
    sa.Column('delay_between_likes_min', sa.Integer(), nullable=False),
    sa.Column('delay_between_likes_max', sa.Integer(), nullable=False),
    sa.Column('default_headless', sa.Boolean(), nullable=False),
    sa.Column('browser_timeout', sa.Integer(), nullable=False),
    sa.Column('max_concurrent_browsers', sa.Integer(), nullable=False),
    sa.Column('auto_rotate_proxies', sa.Boolean(), nullable=False),
    sa.Column('proxy_timeout', sa.Integer(), nullable=False),
    sa.Column('proxy_retry_attempts', sa.Integer(), nullable=False),
    sa.Column('enable_notifications', sa.Boolean(), nullable=False),
    sa.Column('notify_on_task_complete', sa.Boolean(), nullable=False),
    sa.Column('notify_on_errors', sa.Boolean(), nullable=False),
    sa.Column('notify_on_warnings', sa.Boolean(), nullable=False),
    sa.Column('minimize_to_tray', sa.Boolean(), nullable=False),
    sa.Column('start_minimized', sa.Boolean(), nullable=False),
    sa.Column('auto_start_with_system', sa.Boolean(), nullable=False),
    sa.Column('memory_limit_mb', sa.Integer(), nullable=False),
    sa.Column('cpu_limit_percent', sa.Integer(), nullable=False),
    sa.Column('log_retention_days', sa.Integer(), nullable=False),
    sa.Column('encrypt_cookies', sa.Boolean(), nullable=False),
    sa.Column('encrypt_passwords', sa.Boolean(), nullable=False),
    sa.Column('auto_logout_minutes', sa.Integer(), nullable=False),
    sa.Column('advanced_settings', sa.JSON(), nullable=True),
    sa.Column('auto_backup', sa.Boolean(), nullable=False),
    sa.Column('backup_interval_hours', sa.Integer(), nullable=False),
    sa.Column('backup_retention_days', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('tiktok_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=255), nullable=False),
    sa.Column('display_name', sa.String(length=255), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('phone', sa.String(length=50), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_logged_in', sa.Boolean(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('is_banned', sa.Boolean(), nullable=False),
    sa.Column('profile_picture_url', sa.Text(), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('follower_count', sa.Integer(), nullable=False),
    sa.Column('following_count', sa.Integer(), nullable=False),
    sa.Column('likes_count', sa.Integer(), nullable=False),
    sa.Column('videos_count', sa.Integer(), nullable=False),
    sa.Column('browser_profile_id', sa.Integer(), nullable=True),
    sa.Column('cookies_data', sa.Text(), nullable=True),
    sa.Column('session_data', sa.JSON(), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('login_count', sa.Integer(), nullable=False),
    sa.Column('failed_login_attempts', sa.Integer(), nullable=False),
    sa.Column('daily_follow_count', sa.Integer(), nullable=False),
    sa.Column('daily_unfollow_count', sa.Integer(), nullable=False),
    sa.Column('daily_like_count', sa.Integer(), nullable=False),
    sa.Column('daily_comment_count', sa.Integer(), nullable=False),
    sa.Column('last_follow_action', sa.DateTime(), nullable=True),
    sa.Column('last_unfollow_action', sa.DateTime(), nullable=True),
    sa.Column('last_like_action', sa.DateTime(), nullable=True),
    sa.Column('last_comment_action', sa.DateTime(), nullable=True),
    sa.Column('warning_count', sa.Integer(), nullable=False),
    sa.Column('last_warning', sa.DateTime(), nullable=True),
    sa.Column('warning_message', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['browser_profile_id'], ['browser_profiles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tiktok_accounts_browser_profile_id'), 'tiktok_accounts', ['browser_profile_id'], unique=False)
    op.create_index(op.f('ix_tiktok_accounts_id'), 'tiktok_accounts', ['id'], unique=False)
    op.create_index(op.f('ix_tiktok_accounts_username'), 'tiktok_accounts', ['username'], unique=True)
    op.create_table('follow_tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('task_type', sa.Enum('FOLLOW_FOLLOWERS', 'FOLLOW_FOLLOWING', 'UNFOLLOW_USERS', 'LIKE_VIDEOS', 'COMMENT_VIDEOS', name='tasktype'), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'PAUSED', 'COMPLETED', 'FAILED', 'CANCELLED', name='taskstatus'), nullable=False),
    sa.Column('tiktok_account_id', sa.Integer(), nullable=False),
    sa.Column('competitor_id', sa.Integer(), nullable=True),
    sa.Column('browser_profile_id', sa.Integer(), nullable=True),
    sa.Column('target_count', sa.Integer(), nullable=False),
    sa.Column('delay_min', sa.Integer(), nullable=False),
    sa.Column('delay_max', sa.Integer(), nullable=False),
    sa.Column('filter_criteria', sa.JSON(), nullable=True),
    sa.Column('target_usernames', sa.JSON(), nullable=True),
    sa.Column('total_processed', sa.Integer(), nullable=False),
    sa.Column('successful_actions', sa.Integer(), nullable=False),
    sa.Column('failed_actions', sa.Integer(), nullable=False),
    sa.Column('skipped_actions', sa.Integer(), nullable=False),
    sa.Column('scheduled_start', sa.DateTime(), nullable=True),
    sa.Column('actual_start', sa.DateTime(), nullable=True),
    sa.Column('estimated_completion', sa.DateTime(), nullable=True),
    sa.Column('actual_completion', sa.DateTime(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=False),
    sa.Column('current_retries', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('randomize_order', sa.Boolean(), nullable=False),
    sa.Column('respect_rate_limits', sa.Boolean(), nullable=False),
    sa.Column('stop_on_error', sa.Boolean(), nullable=False),
    sa.Column('results_summary', sa.JSON(), nullable=True),
    sa.Column('execution_log', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['browser_profile_id'], ['browser_profiles.id'], ),
    sa.ForeignKeyConstraint(['competitor_id'], ['competitors.id'], ),
    sa.ForeignKeyConstraint(['tiktok_account_id'], ['tiktok_accounts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_follow_tasks_browser_profile_id'), 'follow_tasks', ['browser_profile_id'], unique=False)
    op.create_index(op.f('ix_follow_tasks_competitor_id'), 'follow_tasks', ['competitor_id'], unique=False)
    op.create_index(op.f('ix_follow_tasks_id'), 'follow_tasks', ['id'], unique=False)
    op.create_index(op.f('ix_follow_tasks_name'), 'follow_tasks', ['name'], unique=False)
    op.create_index(op.f('ix_follow_tasks_status'), 'follow_tasks', ['status'], unique=False)
    op.create_index(op.f('ix_follow_tasks_task_type'), 'follow_tasks', ['task_type'], unique=False)
    op.create_index(op.f('ix_follow_tasks_tiktok_account_id'), 'follow_tasks', ['tiktok_account_id'], unique=False)
    op.create_table('activity_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('level', sa.Enum('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL', name='loglevel'), nullable=False),
    sa.Column('activity_type', sa.Enum('LOGIN', 'LOGOUT', 'LOGIN_FAILED', 'PROFILE_CREATED', 'PROFILE_UPDATED', 'PROFILE_DELETED', 'ACCOUNT_ADDED', 'ACCOUNT_UPDATED', 'ACCOUNT_DELETED', 'ACCOUNT_LOGIN', 'ACCOUNT_LOGIN_FAILED', 'FOLLOW_ACTION', 'UNFOLLOW_ACTION', 'LIKE_ACTION', 'COMMENT_ACTION', 'TASK_CREATED', 'TASK_STARTED', 'TASK_PAUSED', 'TASK_RESUMED', 'TASK_COMPLETED', 'TASK_FAILED', 'TASK_CANCELLED', 'COMPETITOR_ADDED', 'COMPETITOR_UPDATED', 'COMPETITOR_DELETED', 'COMPETITOR_SCRAPED', 'SYSTEM_START', 'SYSTEM_STOP', 'SYSTEM_ERROR', 'BROWSER_LAUNCHED', 'BROWSER_CLOSED', 'BROWSER_ERROR', 'PROXY_CONNECTED', 'PROXY_DISCONNECTED', 'PROXY_ERROR', name='activitytype'), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('tiktok_account_id', sa.Integer(), nullable=True),
    sa.Column('browser_profile_id', sa.Integer(), nullable=True),
    sa.Column('follow_task_id', sa.Integer(), nullable=True),
    sa.Column('competitor_id', sa.Integer(), nullable=True),
    sa.Column('extra_data', sa.JSON(), nullable=True),
    sa.Column('error_code', sa.String(length=50), nullable=True),
    sa.Column('error_details', sa.Text(), nullable=True),
    sa.Column('stack_trace', sa.Text(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('duration_ms', sa.Integer(), nullable=True),
    sa.Column('memory_usage_mb', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['browser_profile_id'], ['browser_profiles.id'], ),
    sa.ForeignKeyConstraint(['competitor_id'], ['competitors.id'], ),
    sa.ForeignKeyConstraint(['follow_task_id'], ['follow_tasks.id'], ),
    sa.ForeignKeyConstraint(['tiktok_account_id'], ['tiktok_accounts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_activity_logs_activity_type'), 'activity_logs', ['activity_type'], unique=False)
    op.create_index(op.f('ix_activity_logs_browser_profile_id'), 'activity_logs', ['browser_profile_id'], unique=False)
    op.create_index(op.f('ix_activity_logs_competitor_id'), 'activity_logs', ['competitor_id'], unique=False)
    op.create_index(op.f('ix_activity_logs_created_at'), 'activity_logs', ['created_at'], unique=False)
    op.create_index(op.f('ix_activity_logs_follow_task_id'), 'activity_logs', ['follow_task_id'], unique=False)
    op.create_index(op.f('ix_activity_logs_id'), 'activity_logs', ['id'], unique=False)
    op.create_index(op.f('ix_activity_logs_level'), 'activity_logs', ['level'], unique=False)
    op.create_index(op.f('ix_activity_logs_tiktok_account_id'), 'activity_logs', ['tiktok_account_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_activity_logs_tiktok_account_id'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_level'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_id'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_follow_task_id'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_created_at'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_competitor_id'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_browser_profile_id'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_activity_type'), table_name='activity_logs')
    op.drop_table('activity_logs')
    op.drop_index(op.f('ix_follow_tasks_tiktok_account_id'), table_name='follow_tasks')
    op.drop_index(op.f('ix_follow_tasks_task_type'), table_name='follow_tasks')
    op.drop_index(op.f('ix_follow_tasks_status'), table_name='follow_tasks')
    op.drop_index(op.f('ix_follow_tasks_name'), table_name='follow_tasks')
    op.drop_index(op.f('ix_follow_tasks_id'), table_name='follow_tasks')
    op.drop_index(op.f('ix_follow_tasks_competitor_id'), table_name='follow_tasks')
    op.drop_index(op.f('ix_follow_tasks_browser_profile_id'), table_name='follow_tasks')
    op.drop_table('follow_tasks')
    op.drop_index(op.f('ix_tiktok_accounts_username'), table_name='tiktok_accounts')
    op.drop_index(op.f('ix_tiktok_accounts_id'), table_name='tiktok_accounts')
    op.drop_index(op.f('ix_tiktok_accounts_browser_profile_id'), table_name='tiktok_accounts')
    op.drop_table('tiktok_accounts')
    op.drop_table('user_settings')
    op.drop_index(op.f('ix_proxies_proxy_type'), table_name='proxies')
    op.drop_index(op.f('ix_proxies_name'), table_name='proxies')
    op.drop_index(op.f('ix_proxies_id'), table_name='proxies')
    op.drop_table('proxies')
    op.drop_index(op.f('ix_competitors_username'), table_name='competitors')
    op.drop_index(op.f('ix_competitors_id'), table_name='competitors')
    op.drop_table('competitors')
    op.drop_index(op.f('ix_browser_profiles_proxy_id'), table_name='browser_profiles')
    op.drop_index(op.f('ix_browser_profiles_name'), table_name='browser_profiles')
    op.drop_index(op.f('ix_browser_profiles_id'), table_name='browser_profiles')
    op.drop_table('browser_profiles')
    # ### end Alembic commands ###
