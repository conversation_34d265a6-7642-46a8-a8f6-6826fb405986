"""
Application configuration settings
"""

import os
from pathlib import Path
from typing import Optional

from pydantic_settings import BaseSettings
from pydantic import field_validator


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # App Info
    APP_NAME: str = "TikTok Automation"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # Database
    DATABASE_URL: str = "sqlite:///./tiktok_automation.db"
    DATABASE_ECHO: bool = False
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Redis (for Celery)
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Camoufox Settings
    CAMOUFOX_HEADLESS: bool = True
    CAMOUFOX_TIMEOUT: int = 30000  # 30 seconds
    
    # TikTok Settings
    TIKTOK_BASE_URL: str = "https://www.tiktok.com"
    TIKTOK_API_DELAY_MIN: int = 2  # seconds
    TIKTOK_API_DELAY_MAX: int = 5  # seconds
    TIKTOK_FOLLOW_LIMIT_PER_HOUR: int = 50
    TIKTOK_FOLLOW_LIMIT_PER_DAY: int = 200
    
    # Proxy Settings
    PROXY_TIMEOUT: int = 10  # seconds
    PROXY_RETRY_ATTEMPTS: int = 3
    
    # Performance Settings
    MAX_CONCURRENT_BROWSERS: int = 3
    MAX_CONCURRENT_TASKS: int = 10
    MEMORY_LIMIT_MB: int = 1024  # 1GB
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: Optional[str] = None
    
    # Paths
    DATA_DIR: Path = Path.home() / ".tiktok-automation"
    PROFILES_DIR: Path = DATA_DIR / "profiles"
    COOKIES_DIR: Path = DATA_DIR / "cookies"
    LOGS_DIR: Path = DATA_DIR / "logs"
    
    @field_validator("DATA_DIR", "PROFILES_DIR", "COOKIES_DIR", "LOGS_DIR")
    @classmethod
    def create_directories(cls, v):
        """Create directories if they don't exist"""
        if isinstance(v, Path):
            v.mkdir(parents=True, exist_ok=True)
        return v

    @field_validator("DEBUG", mode="before")
    @classmethod
    def parse_debug(cls, v):
        """Parse DEBUG from string"""
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    @property
    def database_url(self) -> str:
        """Get database URL for alembic"""
        return self.DATABASE_URL

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True,
        "extra": "ignore"  # Ignore extra fields from .env
    }


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings


# Ensure data directories exist
settings.DATA_DIR.mkdir(parents=True, exist_ok=True)
settings.PROFILES_DIR.mkdir(parents=True, exist_ok=True)
settings.COOKIES_DIR.mkdir(parents=True, exist_ok=True)
settings.LOGS_DIR.mkdir(parents=True, exist_ok=True)
