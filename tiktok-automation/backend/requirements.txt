# Core Framework
fastapi>=0.100.0,<0.105.0
uvicorn[standard]>=0.20.0,<0.25.0
pydantic>=2.0.0,<3.0.0
pydantic-settings>=2.0.0,<3.0.0

# Database
sqlalchemy>=1.4.0,<2.1.0
alembic>=1.8.0,<2.0.0
aiosqlite>=0.17.0,<1.0.0

# Async support
aiofiles>=22.0.0,<24.0.0

# HTTP Client
httpx>=0.24.0,<0.26.0
aiohttp>=3.8.0,<4.0.0

# WebSocket
websockets>=10.0,<13.0
python-socketio>=5.0.0,<6.0.0

# Camoufox Integration
camoufox>=0.1.0,<0.3.0
playwright>=1.30.0,<2.0.0

# Security & Encryption
cryptography>=3.4.0,<42.0.0
bcrypt>=3.2.0,<5.0.0
python-jose[cryptography]>=3.0.0,<4.0.0
passlib[bcrypt]>=1.7.0,<2.0.0

# Task Queue
celery>=5.3.4,<6.0.0
redis>=5.0.1,<6.0.0

# Utilities
python-multipart>=0.0.6,<1.0.0
python-dotenv>=1.0.0,<2.0.0
loguru>=0.7.2,<1.0.0
rich>=13.7.0,<14.0.0

# Data Processing
pandas>=1.3.0,<2.1.0
numpy>=1.20.0,<1.25.0

# Proxy Support
requests[socks]>=2.31.0,<3.0.0
pysocks>=1.7.1,<2.0.0

# Browser Automation
selenium>=4.16.0,<5.0.0
undetected-chromedriver>=3.5.4,<4.0.0

# Image Processing (for CAPTCHA)
pillow>=10.1.0,<11.0.0
opencv-python>=********,<5.0.0

# Machine Learning (for behavior patterns)
scikit-learn>=1.3.2,<2.0.0

# Development
pytest>=7.4.3,<8.0.0
pytest-asyncio>=0.21.1,<1.0.0
black>=23.11.0,<24.0.0
isort>=5.12.0,<6.0.0
flake8>=6.1.0,<7.0.0

# Monitoring
psutil>=5.9.6,<6.0.0
memory-profiler>=0.61.0,<1.0.0

# Configuration
pyyaml>=6.0.1,<7.0.0
toml>=0.10.2,<1.0.0
