{"name": "tiktok-automation-frontend", "version": "1.0.0", "description": "React frontend for TikTok Automation Desktop App", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.2", "@mui/x-charts": "^6.18.2", "recharts": "^2.8.0", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "date-fns": "^2.30.0", "lodash": "^4.17.21", "classnames": "^2.3.2"}, "scripts": {"start": "BROWSER=none react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "format": "prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "eslint": "^8.56.0", "prettier": "^3.1.1", "typescript": "^4.9.5"}, "homepage": "./"}